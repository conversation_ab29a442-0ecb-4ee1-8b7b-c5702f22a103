"""
AI Email Service

This service handles the generation of personalized agent performance emails
using AI, following the same pattern as sentiment and accuracy services.
"""

import logging
from typing import Dict
from openai import OpenAI

from app.core.settings import settings
from app.models.email_reporting import Agent<PERSON>mailReport
from app.services.email_service import send_simple_email
from app.models.email import EmailRequest

logger = logging.getLogger(__name__)


def generate_and_send_agent_email(report: AgentEmailReport) -> Dict:
    """
    Generate and send a personalized AI email for an agent.

    Args:
        report: Agent email report containing all performance data

    Returns:
        Dict: Result with success status, email_sent flag, and error info
    """
    try:
        # Initialize OpenAI client
        client = OpenAI(api_key=settings.OPENAI_API_KEY)

        # Prepare agent performance data
        stats = report.summary_stats
        total_duration_minutes = stats.total_duration / 60.0 if stats.total_duration > 0 else 0.0

        # Extract call summaries and key points
        call_summaries = []
        all_key_points = []

        # Accuracy analysis aggregation
        total_missed = 0
        total_incorrect = 0
        total_incomplete = 0
        total_correct = 0
        accuracy_scores = []

        for call in report.call_details:
            if call.summary:
                call_summaries.append(call.summary)
            if call.key_points:
                all_key_points.extend(call.key_points)
            if call.missed_information:
                total_missed += len(call.missed_information)
            if call.incorrect_information:
                total_incorrect += len(call.incorrect_information)
            if call.incomplete_information:
                total_incomplete += len(call.incomplete_information)
            if call.correct_information:
                total_correct += len(call.correct_information)
            if call.accuracy_percentage is not None:
                try:
                    # Try to convert to float - this will fail for any string (NA, na, etc.)
                    accuracy_value = float(call.accuracy_percentage)
                    # Additional check to ensure it's a reasonable percentage (0-100)
                    if 0 <= accuracy_value <= 100:
                        accuracy_scores.append(accuracy_value)
                except (ValueError, TypeError):
                    # Skip any non-numeric values (including "NA", "na", etc.)
                    continue

        # Calculate average accuracy (only from numeric values, excluding NA)
        average_accuracy = sum(accuracy_scores) / len(accuracy_scores) if accuracy_scores else 0

        # Format data for prompt
        sentiment_text = ", ".join([f"{k}: {v}" for k, v in stats.sentiment_breakdown.items()]) \
                        if stats.sentiment_breakdown else "No sentiment data"

        key_points_text = "\n".join([f"• {point}" for point in all_key_points[:10]]) \
                         if all_key_points else "No key points available"

        call_summaries_text = "\n".join([f"• {summary[:200]}..." if len(summary) > 200 else f"• {summary}"
                                       for summary in call_summaries[:5]]) \
                             if call_summaries else "No call summaries available"

        # Create system prompt
        system_prompt = """You are a professional email assistant who crafts warm, visually appealing, and highly personalized performance summary emails for call center agents. Your tone must always be:

- Encouraging, appreciative, and human  
- Motivational and forward-looking, never robotic or repetitive  
- Creative and constructive, offering specific suggestions in a conversational way  
- Professional but approachable — not stiff or overly formal  

📌 Your performance summaries must follow this structure and style:

1. START WITH GRATITUDE  
   Open with a friendly greeting and express genuine appreciation for the agent’s efforts.

2. CALL VOLUME RECOGNITION  
   Mention the total number of calls handled (e.g., “you handled 42 calls”) — but do NOT include any percentages, sentiment breakdowns, or counts of errors.

3. SHARED MOMENTS THAT STOOD OUT  
   Dynamically summarize memorable, recurring, or impactful themes from the calls. Use a concise bullet list where each item is an explanatory sentence between 15 to 60 words. Make each point feel personal, thoughtful, and engaging.

4. SMALL SHIFTS, BIG IMPACT  
   Offer helpful, actionable ideas that the agent can easily reflect on or incorporate. Use explanatory bullet points — not short phrases. Each point should guide, not just inform.

5. PRESENT GROWTH IDEAS  
   Share 2–3 improvement suggestions framed as exciting growth opportunities, not critiques. These too should be phrased in explanatory, encouraging bullet points.

6. CLOSE WITH INSPIRATION  
   End with a motivational message that instills confidence, pride, and a sense of momentum.

📩 STYLE & FORMATTING RULES:

- Output in HTML only — start with `<!DOCTYPE html>` and end with `</html>`  
- Do NOT use markdown formatting  
- Use inline CSS for clean, attractive formatting: clear spacing, bullet lists, readable fonts, and soft color highlights  
- Use `font-family: Georgia, serif;` for all text  
- Keep the word count between 300–500 words  
- Rotate vocabulary and sentence structure to keep the tone fresh and personalized  
- Never refer to the message as a "report" or "feedback" — this is a **supportive, forward-looking summary**  
- Avoid robotic phrasing or generic patterns — every line should feel intentional and authentic  

✨ TONE GUIDELINES:

- Rotate sentence openings — don’t start every paragraph the same way  
- Use words like: “amazing”, “exciting”, “we love how you...”, “fantastic to see”, “a great moment was...”, “keep shining with...”  
- Write as if a caring team leader is speaking directly to the agent  
- Emphasize the agent’s value, potential, and progress  

Below is the HTML structure you must follow and customize each time:

<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8">
    <title>Performance Summary</title>
  </head>
  <body style="font-family:'Segoe UI', sans-serif; line-height: 1.6; color: #333; background-color: #f9f9f9; padding: 30px;">
    
    <div style="background-color: #ffffff; border-radius: 10px; padding: 25px; box-shadow: 0 2px 8px rgba(0,0,0,0.05);">
      
      <p style="font-size: 16px;">Hi {agent_name},</p>

      <p style="margin-bottom: 1em;">
        Just a quick note to say how much we appreciate the energy and care you bring to your work. Your dedication truly makes a difference, and it's a joy to watch you grow in your role.
      </p>

      <p style="margin-bottom: 1em;">
        Over the recent period, you handled <strong>{total_calls} calls</strong> — each one offering a moment to connect, support, and solve. That’s something to be proud of!
      </p>

      <p style="font-weight: bold; color: #2E86C1; margin-top: 1.5em;">✨ Shared Moments That Stood Out:</p>
      <ul style="margin-top: 0.5em; padding-left: 20px;">
        {key_topics_list}
      </ul>

      <p style="font-weight: bold; color: #2E86C1; margin-top: 1.5em;">🌱 Small Shifts, Big Impact:</p>
      <ul style="margin-top: 0.5em; padding-left: 20px;">
        {actionable_items_list}
      </ul>

      <p style="font-weight: bold; color: #2E86C1; margin-top: 1.5em;">🚀 Opportunities for Growth:</p>
      <ul style="margin-top: 0.5em; padding-left: 20px;">
        {improvement_ideas_list}
      </ul>

      <p style="margin-top: 1.5em;">
        Keep leaning into your strengths — your ability to stay present, learn quickly, and care deeply is what sets you apart. Every call is a chance to grow and shine, and you're doing just that.
      </p>

      <p style="margin-top: 1em;">Thanks again for all that you do,</p>
      <p><strong>– The Team</strong></p>
    </div>

  </body>
</html>

"""

        # Create user prompt
        user_prompt = f"""Generate a personalized performance summary email for the following  counsellor:

**Agent Information:**
- Name: {report.agent_name}
- Reporting Period: {report.report_period_start} to {report.report_period_end}

**Performance Metrics:**
- Total Calls: {stats.total_calls}
- Calls with Transcription: {stats.calls_with_transcription}
- Sentiment Breakdown: {sentiment_text}

**Accuracy Analysis:**
- Correct Information Items: {total_correct}
- Missed Information Items: {total_missed}
- Incorrect Information Items: {total_incorrect}
- Incomplete Information Items: {total_incomplete}

**Key Points from Calls:**
{key_points_text}

**Call Summaries (for context):**
{call_summaries_text}

Email Requirements:
- Start with appreciation for the agent's work and engagement.
- Mention total calls handled during the period.
- Present the key discussion themes from the calls (as bullet points).
- Present action-oriented suggestions (as bullet points).
- Summarize performance in an uplifting, forward-looking tone.
- Use professional provided HTML structure and visual formatting.
- Do not include accuracy or sentiment statistics, or any breakdowns of incorrect, missed, or incomplete information.
- The tone should be warm, sincere, and empowering.

The email should be 300–500 words and genuinely motivating."""

        # Call OpenAI API
        response = client.chat.completions.create(
            model=settings.OPENAI_MODEL,
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ],
            temperature=0.3,  # Slightly higher temperature for more creative emails
        )

        # Extract the generated content
        generated_content = response.choices[0].message.content

        # Generate subject line
        subject = f"Performance Insights & Appreciation - {report.agent_name} ({report.report_period_start} to {report.report_period_end})"

        # Send email
        email_request = EmailRequest(
            to_email=report.agent_email,
            subject=subject,
            message=generated_content
        )

        email_response = send_simple_email(email_request)

        if email_response.success:
            logger.info(f"AI-generated email sent successfully to {report.agent_name}")
            return {
                "agent_name": report.agent_name,
                "success": True,
                "email_sent": True,
                "ai_email_generated": True,
                "error": None
            }
        else:
            logger.error(f"Failed to send AI email to {report.agent_name}: {email_response.message}")
            return {
                "agent_name": report.agent_name,
                "success": False,
                "email_sent": False,
                "ai_email_generated": True,
                "error": f"Email sending failed: {email_response.message}"
            }

    except Exception as e:
        logger.error(f"Error generating AI email for {report.agent_name}: {str(e)}")

        # Try fallback email
        try:
            fallback_email = create_fallback_email(report)
            subject = f"Performance Report - {report.agent_name} ({report.report_period_start} to {report.report_period_end})"

            email_request = EmailRequest(
                to_email=report.agent_email,
                subject=subject,
                message=fallback_email
            )

            email_response = send_simple_email(email_request)

            if email_response.success:
                logger.info(f"Fallback email sent successfully to {report.agent_name}")
                return {
                    "agent_name": report.agent_name,
                    "success": True,
                    "email_sent": True,
                    "ai_email_generated": False,
                    "error": f"AI generation failed, fallback used: {str(e)}"
                }
            else:
                return {
                    "agent_name": report.agent_name,
                    "success": False,
                    "email_sent": False,
                    "ai_email_generated": False,
                    "error": f"Both AI and fallback failed: {str(e)}, {email_response.message}"
                }
        except Exception as fallback_error:
            return {
                "agent_name": report.agent_name,
                "success": False,
                "email_sent": False,
                "ai_email_generated": False,
                "error": f"Complete failure: {str(e)}, {str(fallback_error)}"
            }


def create_fallback_email(report: AgentEmailReport) -> str:
    """
    Create a fallback email in case AI generation fails.
    This ensures agents still receive feedback even if AI is unavailable.
    """
    stats = report.summary_stats

    # Calculate some basic metrics
    total_duration_minutes = stats.total_duration / 60.0 if stats.total_duration > 0 else 0.0
    avg_duration_minutes = stats.average_duration / 60.0 if stats.average_duration > 0 else 0.0

    # Build sentiment summary
    sentiment_summary = ""
    if stats.sentiment_breakdown:
        sentiment_items = [f"{sentiment.title()}: {count}" for sentiment, count in stats.sentiment_breakdown.items()]
        sentiment_summary = ", ".join(sentiment_items)

    return f"""<html>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 800px; margin: 0 auto; padding: 20px;">
    <h2 style="color: #2c5aa0; border-bottom: 2px solid #2c5aa0; padding-bottom: 10px;">
        Performance Report - {report.agent_name}
    </h2>

    <p style="font-size: 16px; margin: 20px 0;">
        Dear {report.agent_name},
    </p>

    <p style="margin: 15px 0;">
        Thank you for your dedication and hard work during the period from
        <strong>{report.report_period_start}</strong> to <strong>{report.report_period_end}</strong>.
        Your commitment to providing excellent service is truly appreciated.
    </p>

    <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
        <h3 style="color: #2c5aa0; margin-top: 0;">📊 Your Performance Highlights</h3>
        <ul style="list-style-type: none; padding: 0;">
            <li style="margin: 10px 0; padding: 8px; background-color: white; border-radius: 4px;">
                <strong>📞 Total Calls:</strong> {stats.total_calls}
            </li>
            <li style="margin: 10px 0; padding: 8px; background-color: white; border-radius: 4px;">
                <strong>⏱️ Total Talk Time:</strong> {total_duration_minutes:.1f} minutes
            </li>
            <li style="margin: 10px 0; padding: 8px; background-color: white; border-radius: 4px;">
                <strong>📝 Calls with Documentation:</strong> {stats.calls_with_transcription}
            </li>
            <li style="margin: 10px 0; padding: 8px; background-color: white; border-radius: 4px;">
                <strong>📈 Average Call Duration:</strong> {avg_duration_minutes:.1f} minutes
            </li>
            {f'<li style="margin: 10px 0; padding: 8px; background-color: white; border-radius: 4px;"><strong>😊 Sentiment Analysis:</strong> {sentiment_summary}</li>' if sentiment_summary else ''}
        </ul>
    </div>

    <div style="background-color: #e8f5e8; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #28a745;">
        <h3 style="color: #155724; margin-top: 0;">🌟 Keep Up the Great Work!</h3>
        <p style="margin: 10px 0;">
            Your consistent effort in handling {stats.total_calls} calls shows your commitment to our team and customers.
            Every interaction you have makes a positive impact.
        </p>
        <p style="margin: 10px 0;">
            Continue focusing on clear communication and thorough documentation.
            Your attention to detail in capturing call information is valuable for our continuous improvement.
        </p>
    </div>

    <p style="margin: 20px 0;">
        If you have any questions about your performance or would like to discuss ways to further enhance your skills,
        please don't hesitate to reach out to your supervisor.
    </p>

    <p style="margin: 20px 0;">
        Thank you again for your excellent work and dedication!
    </p>

    <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd;">
        <p style="margin: 5px 0;"><strong>Best regards,</strong></p>
        <p style="margin: 5px 0; color: #2c5aa0;"><strong>Performance Management Team</strong></p>
    </div>

    <div style="margin-top: 30px; padding: 15px; background-color: #f8f9fa; border-radius: 4px; font-size: 12px; color: #666;">
        <p style="margin: 0;">
            This is an automated performance report. For questions or concerns, please contact your supervisor.
        </p>
    </div>
</body>
</html>"""
