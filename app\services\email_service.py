from app.utils.email_sending import SimpleEmailSender
from app.core.settings import settings
from app.models.email import EmailRequest, EmailWithAttachmentRequest, EmailResponse
import logging
import os

logger = logging.getLogger(__name__)


def validate_email_configuration() -> bool:
    """
    Validate that email configuration is properly set up.
    
    Returns:
        bool: True if email configuration is valid, False otherwise
    """
    if not settings.SMTP_EMAIL or not settings.SMTP_PASSWORD:
        logger.error("Email configuration is missing. Please set SMTP_EMAIL and SMTP_PASSWORD in environment variables.")
        return False
    return True


def send_simple_email(email_request: EmailRequest) -> EmailResponse:
    """
    Send a simple email without attachment.
    
    Args:
        email_request: EmailRequest containing email details
        
    Returns:
        EmailResponse: Response indicating success or failure
    """
    try:
        # Validate email configuration
        if not validate_email_configuration():
            return EmailResponse(
                success=False,
                message="Email configuration is not properly set up. Please contact administrator.",
                recipient=email_request.to_email
            )
        
        # Create email sender instance
        email_sender = SimpleEmailSender(
            email=settings.SMTP_EMAIL,
            password=settings.SMTP_PASSWORD
        )
        
        # Send email
        success = email_sender.send_simple_email(
            to_email=email_request.to_email,
            subject=email_request.subject,
            message=email_request.message
        )
        
        if success:
            logger.info(f"Email sent successfully to {email_request.to_email}")
            return EmailResponse(
                success=True,
                message="Email sent successfully",
                recipient=email_request.to_email
            )
        else:
            logger.error(f"Failed to send email to {email_request.to_email}")
            return EmailResponse(
                success=False,
                message="Failed to send email. Please check the logs for more details.",
                recipient=email_request.to_email
            )
            
    except Exception as e:
        logger.error(f"Error sending email to {email_request.to_email}: {str(e)}")
        return EmailResponse(
            success=False,
            message=f"Error sending email: {str(e)}",
            recipient=email_request.to_email
        )


def send_email_with_attachment(email_request: EmailWithAttachmentRequest) -> EmailResponse:
    """
    Send an email with Excel attachment.
    
    Args:
        email_request: EmailWithAttachmentRequest containing email details and file path
        
    Returns:
        EmailResponse: Response indicating success or failure
    """
    try:
        # Validate email configuration
        if not validate_email_configuration():
            return EmailResponse(
                success=False,
                message="Email configuration is not properly set up. Please contact administrator.",
                recipient=email_request.to_email
            )
        
        # Validate file exists
        if not os.path.exists(email_request.excel_file_path):
            logger.error(f"Excel file not found: {email_request.excel_file_path}")
            return EmailResponse(
                success=False,
                message=f"Excel file not found: {email_request.excel_file_path}",
                recipient=email_request.to_email
            )
        
        # Create email sender instance
        email_sender = SimpleEmailSender(
            email=settings.SMTP_EMAIL,
            password=settings.SMTP_PASSWORD
        )
        
        # Send email with attachment
        success = email_sender.send_email_with_excel(
            to_email=email_request.to_email,
            subject=email_request.subject,
            message=email_request.message,
            excel_file_path=email_request.excel_file_path
        )
        
        if success:
            logger.info(f"Email with attachment sent successfully to {email_request.to_email}")
            return EmailResponse(
                success=True,
                message="Email with attachment sent successfully",
                recipient=email_request.to_email
            )
        else:
            logger.error(f"Failed to send email with attachment to {email_request.to_email}")
            return EmailResponse(
                success=False,
                message="Failed to send email with attachment. Please check the logs for more details.",
                recipient=email_request.to_email
            )
            
    except Exception as e:
        logger.error(f"Error sending email with attachment to {email_request.to_email}: {str(e)}")
        return EmailResponse(
            success=False,
            message=f"Error sending email with attachment: {str(e)}",
            recipient=email_request.to_email
        )
