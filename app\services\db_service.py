from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_
from app.db.models import (
    Transcription, SpeakerSegmentDB, SentimentAnalysisDB,
    InformationAccuracyDB, CallData, TranscriptionFailure as TranscriptionFailureDB,
    ConversationFlagDB, ConversationToneDB, SentimentEnum, ConversationFlagEnum, ConversationToneEnum
)
from app.models.transcription import TranscriptionResponse, SpeakerSegment, SentimentAnalysis, InformationAccuracy
from app.models.call_data import ProcessedCallData, TranscriptionFailure
from app.models.filters import CallFilters, FilterType
from typing import List, Dict, Any, Optional
from datetime import datetime, date, timedelta
import logging

logger = logging.getLogger(__name__)


def _build_filter_conditions(filters: Optional[CallFilters]):
    """
    Build SQLAlchemy filter conditions based on CallFilters.

    Args:
        filters: CallFilters object containing filtering parameters

    Returns:
        Tuple of (conditions, sentiment_conditions, flag_conditions)
    """
    conditions = []

    if not filters:
        return conditions, [], []

    # Date filtering
    if filters.filter_type != FilterType.ALL:
        today = date.today()

        if filters.filter_type == FilterType.TODAY:
            target_date = today.strftime('%Y-%m-%d')
            conditions.append(CallData.call_date == target_date)

        elif filters.filter_type == FilterType.YESTERDAY:
            yesterday = today - timedelta(days=1)
            target_date = yesterday.strftime('%Y-%m-%d')
            conditions.append(CallData.call_date == target_date)

        elif filters.filter_type == FilterType.SPECIFIC_DAY and filters.specific_date:
            target_date = filters.specific_date.strftime('%Y-%m-%d')
            conditions.append(CallData.call_date == target_date)

        elif filters.filter_type == FilterType.DATE_RANGE and filters.start_date and filters.end_date:
            start_date_str = filters.start_date.strftime('%Y-%m-%d')
            end_date_str = filters.end_date.strftime('%Y-%m-%d')
            conditions.append(and_(
                CallData.call_date >= start_date_str,
                CallData.call_date <= end_date_str
            ))

    # Agent name filtering - case-insensitive partial matching
    if filters.agent_name:
        # Use ilike for case-insensitive partial matching (PostgreSQL)
        # This handles both uppercase and lowercase issues
        conditions.append(CallData.agent_name.ilike(f"%{filters.agent_name}%"))

    # Sentiment filtering - requires join with SentimentAnalysisDB
    sentiment_conditions = []

    if filters.sentiment_filter:
        # Convert filter values to enum values for better filtering
        sentiment_enum_values = []
        for s in filters.sentiment_filter:
            if hasattr(s, 'value'):
                # It's an enum object, get the string value
                filter_value = s.value
            else:
                # It's already a string
                filter_value = s

            # Convert to SentimentEnum
            try:
                sentiment_enum_values.append(SentimentEnum(filter_value))
            except ValueError:
                logger.warning(f"Invalid sentiment filter value: {filter_value}")
                continue

        if sentiment_enum_values:
            logger.info(f"Applying sentiment filter with enum values: {[e.value for e in sentiment_enum_values]}")
            sentiment_conditions.append(SentimentAnalysisDB.overall_sentiment.in_(sentiment_enum_values))

    # Conversation flags filtering - requires join with ConversationFlagDB
    flag_conditions = []
    if filters.conversation_flags:
        # Convert filter values to enum values for better filtering
        flag_enum_values = []
        for flag in filters.conversation_flags:
            if hasattr(flag, 'value'):
                # It's an enum object, get the string value
                filter_value = flag.value
            else:
                # It's already a string
                filter_value = flag

            # Convert to ConversationFlagEnum (ensure lowercase)
            try:
                filter_value_lower = filter_value.lower()
                flag_enum_values.append(ConversationFlagEnum(filter_value_lower))
            except ValueError:
                logger.warning(f"Invalid conversation flag filter value: {filter_value}")
                continue

        if flag_enum_values:
            logger.info(f"Applying conversation flags filter with enum values: {[e.value for e in flag_enum_values]}")
            # Use the new ConversationFlagDB table for filtering
            # Filter for records where the flag is present (is_present = True)
            flag_conditions.append(
                and_(
                    ConversationFlagDB.flag_type.in_(flag_enum_values),
                    ConversationFlagDB.is_present == True
                )
            )

    return conditions, sentiment_conditions, flag_conditions


def save_transcription(
    db: Session,
    filename: str,
    transcription_response: TranscriptionResponse
) -> str:
    """
    Save transcription data to the database.

    Args:
        db: Database session
        filename: Name of the audio file
        transcription_response: TranscriptionResponse object

    Returns:
        str: ID of the created transcription record
    """
    try:
        # Create transcription record
        transcription = Transcription(
            filename=filename,
            text=transcription_response.text,
            transcription_metadata=transcription_response.metadata
        )

        db.add(transcription)
        db.flush()  # Flush to get the ID

        # Save speaker segments if available
        if transcription_response.speaker_segments:
            save_speaker_segments(
                db=db,
                transcription_id=transcription.id,
                speaker_segments=transcription_response.speaker_segments
            )

        # Save sentiment analysis if available
        if transcription_response.sentiment_analysis:
            save_sentiment_analysis(
                db=db,
                transcription_id=transcription.id,
                sentiment_analysis=transcription_response.sentiment_analysis
            )

        db.commit()
        return transcription.id

    except Exception as e:
        db.rollback()
        logger.error(f"Error saving transcription to database: {str(e)}")
        raise


def save_speaker_segments(
    db: Session,
    transcription_id: str,
    speaker_segments: List[SpeakerSegment]
) -> None:
    """
    Save speaker segments to the database.

    Args:
        db: Database session
        transcription_id: ID of the transcription
        speaker_segments: List of SpeakerSegment objects
    """
    for segment in speaker_segments:
        speaker_segment_db = SpeakerSegmentDB(
            transcription_id=transcription_id,
            speaker=segment.speaker,
            text=segment.text
        )
        db.add(speaker_segment_db)


def save_sentiment_analysis(
    db: Session,
    transcription_id: str,
    sentiment_analysis: SentimentAnalysis
) -> None:
    """
    Save sentiment analysis to the database using the new enum-based storage.

    Args:
        db: Database session
        transcription_id: ID of the transcription
        sentiment_analysis: SentimentAnalysis object
    """
    # Convert enum values to database enums for better storage and filtering
    overall_sentiment_enum = None
    if sentiment_analysis.overall_sentiment:
        try:
            overall_sentiment_enum = SentimentEnum(sentiment_analysis.overall_sentiment.value)
        except ValueError:
            logger.warning(f"Invalid sentiment value: {sentiment_analysis.overall_sentiment.value}")

    sentiment_analysis_db = SentimentAnalysisDB(
        transcription_id=transcription_id,
        is_student_university_conversation=sentiment_analysis.is_student_university_conversation,
        overall_sentiment=overall_sentiment_enum,
        relevant_category=sentiment_analysis.relevant_category,
        key_points=sentiment_analysis.key_points,
        speaker_roles={k: v.value for k, v in sentiment_analysis.speaker_roles.items()} if sentiment_analysis.speaker_roles else {},
        summary=sentiment_analysis.summary,
        action_items=sentiment_analysis.action_items
    )

    db.add(sentiment_analysis_db)


def save_conversation_flags(
    db: Session,
    transcription_id: str,
    conversation_flags: Dict[str, bool]
) -> None:
    """
    Save conversation flags to the database using the new separate table.

    Args:
        db: Database session
        transcription_id: ID of the transcription
        conversation_flags: Dictionary of flag names and their boolean values
    """
    for flag_name, is_present in conversation_flags.items():
        try:
            # Ensure flag name is lowercase to match enum values
            flag_name_lower = flag_name.lower()

            # Convert string flag name to enum
            flag_enum = ConversationFlagEnum(flag_name_lower)

            # Create a record for this flag
            flag_db = ConversationFlagDB(
                transcription_id=transcription_id,
                flag_type=flag_enum,
                is_present=is_present
            )

            db.add(flag_db)

        except ValueError as e:
            logger.warning(f"Invalid conversation flag: {flag_name}, error: {e}")
            continue


def save_conversation_tone(
    db: Session,
    transcription_id: str,
    conversation_tone: Optional[str]
) -> None:
    """
    Save conversation tone to the database.

    Args:
        db: Database session
        transcription_id: ID of the transcription
        conversation_tone: Tone string (pleasant, dull, etc.) or None
    """
    if conversation_tone:
        try:
            # Convert string tone to enum (ensure lowercase)
            tone_lower = conversation_tone.lower()
            tone_enum = ConversationToneEnum(tone_lower)

            # Create a record for this tone
            tone_db = ConversationToneDB(
                transcription_id=transcription_id,
                tone=tone_enum
            )

            db.add(tone_db)

        except ValueError:
            logger.warning(f"Invalid conversation tone: {conversation_tone}")
    else:
        # If no tone provided, still create a record with None tone
        tone_db = ConversationToneDB(
            transcription_id=transcription_id,
            tone=None
        )
        db.add(tone_db)


def get_conversation_tone(db: Session, transcription_id: str) -> Optional[str]:
    """
    Get conversation tone for a transcription.

    Args:
        db: Database session
        transcription_id: ID of the transcription

    Returns:
        Optional[str]: Tone string or None if not found
    """
    tone_record = db.query(ConversationToneDB).filter(
        ConversationToneDB.transcription_id == transcription_id
    ).first()

    if tone_record and tone_record.tone:
        return tone_record.tone.value
    return None


def save_information_accuracy(
    db: Session,
    transcription_id: str,
    information_accuracy: InformationAccuracy
) -> None:
    """
    Save information accuracy analysis to the database.
    Note: Conversation flags are now saved separately using save_conversation_flags().

    Args:
        db: Database session
        transcription_id: ID of the transcription
        information_accuracy: InformationAccuracy object
    """
    information_accuracy_db = InformationAccuracyDB(
        transcription_id=transcription_id,
        missed_information=information_accuracy.missed_information,
        incorrect_information=information_accuracy.incorrect_information,
        incomplete_information=information_accuracy.incomplete_information,
        correct_information=information_accuracy.correct_information,
        overall_assessment=information_accuracy.overall_assessment,
        accuracy_percentage=information_accuracy.accuracy_percentage
    )

    db.add(information_accuracy_db)

    # Save conversation flags separately
    if information_accuracy.conversation_flags:
        save_conversation_flags(db, transcription_id, information_accuracy.conversation_flags)

    # Save conversation tone separately
    save_conversation_tone(db, transcription_id, information_accuracy.conversation_tone)


def save_call_data(db: Session, processed_call: ProcessedCallData) -> str:
    """
    Save call data to the database.

    Args:
        db: Database session
        processed_call: ProcessedCallData object

    Returns:
        str: ID of the created call data record
    """
    try:
        # Check if call data already exists for this call ID
        existing_call = db.query(CallData).filter(CallData.call_id == processed_call.call_id).first()

        if existing_call:
            # Update existing record with all fields
            existing_call.call_duration = processed_call.call_duration
            existing_call.audio_url = processed_call.audio_url
            existing_call.transcription_id = processed_call.transcription_id
            existing_call.processed = processed_call.processed

            # Update additional fields
            existing_call.agent_name = processed_call.agent_name
            existing_call.call_date = processed_call.call_date
            existing_call.call_time = processed_call.call_time
            existing_call.group_name = processed_call.group_name
            existing_call.student_id = processed_call.student_id
            existing_call.student_name = processed_call.student_name
            existing_call.call_direction = processed_call.call_direction
            existing_call.call_source = processed_call.call_source

            db.commit()
            return existing_call.id

        # Create new call data record with additional fields
        call_data = CallData(
            call_id=processed_call.call_id,
            call_duration=processed_call.call_duration,
            audio_url=processed_call.audio_url,
            transcription_id=processed_call.transcription_id,
            processed=processed_call.processed,
            agent_name=processed_call.agent_name,
            call_date=processed_call.call_date,
            call_time=processed_call.call_time,
            group_name=processed_call.group_name,
            student_id=processed_call.student_id,
            student_name=processed_call.student_name,
            call_direction=processed_call.call_direction,
            call_source=processed_call.call_source
        )


        db.add(call_data)
        db.commit()
        db.refresh(call_data)

        return call_data.id

    except Exception as e:
        db.rollback()
        logger.error(f"Error saving call data to database: {str(e)}")
        raise


def get_call_data_by_call_id(db: Session, call_id: str) -> Optional[ProcessedCallData]:
    """
    Get call data by call ID.

    Args:
        db: Database session
        call_id: Call ID

    Returns:
        Optional[ProcessedCallData]: ProcessedCallData object if found, None otherwise
    """
    try:
        call_data = db.query(CallData).filter(CallData.call_id == call_id).first()

        if not call_data:
            return None

        return ProcessedCallData(
            call_id=call_data.call_id,
            call_duration=call_data.call_duration,
            audio_url=call_data.audio_url,
            transcription_id=call_data.transcription_id,
            processed=call_data.processed,
            created_at=call_data.created_at
        )

    except Exception as e:
        logger.error(f"Error retrieving call data from database: {str(e)}")
        return None


def get_call_data_detail_by_id(db: Session, call_data_id: str) -> Optional[Dict[str, Any]]:
    """
    Get detailed call data by database ID with transcription information.

    Args:
        db: Database session
        call_data_id: Database ID of the call data record

    Returns:
        Optional[Dict[str, Any]]: Call data dictionary with transcription details if found, None otherwise
    """
    try:

        # Query call data with transcription join
        call_data = db.query(CallData).outerjoin(
            Transcription, CallData.transcription_id == Transcription.id
        ).filter(CallData.id == call_data_id).first()


        if not call_data:
            return None

        # Create base call data dictionary
        call_info = {
            "id": call_data.id,
            "call_id": call_data.call_id,
            "call_duration": call_data.call_duration,
            "audio_url": call_data.audio_url,
            "processed": call_data.processed,
            "created_at": call_data.created_at,
            "transcription_id": call_data.transcription_id,
            "transcription": None
        }

        # Add transcription details if available
        if call_data.transcription_id and call_data.transcription:
            transcription = call_data.transcription

            # Get speaker segments count
            speaker_count = len(transcription.speaker_segments) if transcription.speaker_segments else 0

            # Get sentiment info if available
            sentiment_info = None
            if transcription.sentiment_analysis:
                sa = transcription.sentiment_analysis
                sentiment_info = {
                    "overall_sentiment": sa.overall_sentiment,
                    "is_student_university_conversation": sa.is_student_university_conversation,
                    "relevant_category": sa.relevant_category,
                    "summary": sa.summary
                }

            # Get information accuracy if available
            accuracy_info = None
            if transcription.information_accuracy:
                ia = transcription.information_accuracy

                # Get conversation flags from the separate table
                conversation_flags = {
                    "satisfaction": False,
                    "confusion": False,
                    "urgency": False,
                    "frustration": False,
                    "abusive": False
                }

                # Query the conversation flags for this transcription
                flag_records = db.query(ConversationFlagDB).filter(
                    ConversationFlagDB.transcription_id == transcription.id
                ).all()

                for flag_record in flag_records:
                    if flag_record.flag_type and flag_record.is_present:
                        conversation_flags[flag_record.flag_type.value] = True

                # Get conversation tone
                conversation_tone = get_conversation_tone(db, transcription.id)

                accuracy_info = {
                    "overall_assessment": ia.overall_assessment[:100] + "..."
                        if ia.overall_assessment and len(ia.overall_assessment) > 100
                        else ia.overall_assessment,
                    "missed_count": len(ia.missed_information) if ia.missed_information else 0,
                    "incorrect_count": len(ia.incorrect_information) if ia.incorrect_information else 0,
                    "accuracy_percentage": ia.accuracy_percentage if ia.accuracy_percentage is not None else "NA",
                    "conversation_flags": conversation_flags,
                    "conversation_tone": conversation_tone
                }

            # Add transcription details to call info
            call_info["transcription"] = {
                "id": transcription.id,
                "text_preview": transcription.text[:150] + "..." if len(transcription.text) > 150 else transcription.text,
                "duration_seconds": 0.0,  # Default value since column was removed from database
                "created_at": transcription.created_at,
                "speaker_count": speaker_count,
                "sentiment_info": sentiment_info,
                "accuracy_info": accuracy_info
            }

        return call_info

    except Exception as e:
        logger.error(f"Error retrieving call data detail from database: {str(e)}")
        return None


def save_transcription_failure(
    db: Session,
    call_id: str,
    error_message: str,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    audio_url: Optional[str] = None
) -> str:
    """
    Save a transcription failure record to the database.

    Args:
        db: Database session
        call_id: Call ID that failed transcription
        error_message: Error message or reason for transcription failure
        start_date: Start date of the call (optional)
        end_date: End date of the call (optional)
        audio_url: URL of the audio file that failed transcription (optional)

    Returns:
        str: ID of the created failure record
    """
    try:
        # Create a new transcription failure record
        failure = TranscriptionFailureDB(
            call_id=call_id,
            start_date=start_date,
            end_date=end_date,
            error_message=error_message,
            audio_url=audio_url
        )

        db.add(failure)
        db.commit()
        db.refresh(failure)

        logger.info(f"Transcription failure for call {call_id} saved with ID: {failure.id}")
        return failure.id

    except Exception as e:
        db.rollback()
        logger.error(f"Error saving transcription failure to database: {str(e)}")
        raise


def get_transcription_failure_by_call_id(db: Session, call_id: str) -> Optional[TranscriptionFailure]:
    """
    Get transcription failures for a specific call ID.

    Args:
        db: Database session
        call_id: Call ID to search for

    Returns:
        Optional[TranscriptionFailure]: The most recent failure record for the call ID, or None if not found
    """
    try:
        failure = db.query(TranscriptionFailureDB).filter(
            TranscriptionFailureDB.call_id == call_id
        ).order_by(TranscriptionFailureDB.created_at.desc()).first()

        if not failure:
            return None

        return TranscriptionFailure(
            id=failure.id,
            call_id=failure.call_id,
            start_date=failure.start_date,
            end_date=failure.end_date,
            error_message=failure.error_message,
            audio_url=failure.audio_url,
            created_at=failure.created_at
        )

    except Exception as e:
        logger.error(f"Error retrieving transcription failure from database: {str(e)}")
        return None


def get_call_data_detail_by_call_id(db: Session, call_id: str) -> Optional[Dict[str, Any]]:
    """
    Get detailed call data by call_id with transcription information.

    Args:
        db: Database session
        call_id: Call ID (not database ID)

    Returns:
        Optional[Dict[str, Any]]: Call data dictionary with transcription details if found, None otherwise
    """
    try:
        # Query call data with transcription join
        call_data = db.query(CallData).outerjoin(
            Transcription, CallData.transcription_id == Transcription.id
        ).filter(CallData.call_id == call_id).first()

        if not call_data:
            return None

        # Create base call data dictionary with additional fields
        call_info = {
            "id": call_data.id,
            "call_id": call_data.call_id,
            "call_duration": call_data.call_duration,
            "audio_url": call_data.audio_url,
            "processed": call_data.processed,
            "created_at": call_data.created_at,
            "transcription_id": call_data.transcription_id,
            "transcription": None,
            "agent_name": call_data.agent_name,
            "call_date": call_data.call_date,
            "call_time": call_data.call_time,
            "group_name": call_data.group_name,
            "student_id": call_data.student_id,
            "student_name": call_data.student_name,
            "call_direction": call_data.call_direction,
            "call_source": call_data.call_source
        }

        # Add transcription details if available
        if call_data.transcription_id and call_data.transcription:
            transcription = call_data.transcription

            # Get speaker segments count
            speaker_count = len(transcription.speaker_segments) if transcription.speaker_segments else 0

            # Get sentiment info if available
            sentiment_info = None
            if transcription.sentiment_analysis:
                sa = transcription.sentiment_analysis
                sentiment_info = {
                    "overall_sentiment": sa.overall_sentiment,
                    "is_student_university_conversation": sa.is_student_university_conversation,
                    "relevant_category": sa.relevant_category,
                    "summary": sa.summary
                }

            # Get information accuracy if available
            accuracy_info = None
            if transcription.information_accuracy:
                ia = transcription.information_accuracy

                # Get conversation flags from the separate table
                conversation_flags = {
                    "satisfaction": False,
                    "confusion": False,
                    "urgency": False,
                    "frustration": False,
                    "abusive": False
                }

                # Query the conversation flags for this transcription
                flag_records = db.query(ConversationFlagDB).filter(
                    ConversationFlagDB.transcription_id == transcription.id
                ).all()

                for flag_record in flag_records:
                    if flag_record.flag_type and flag_record.is_present:
                        conversation_flags[flag_record.flag_type.value] = True

                # Get conversation tone
                conversation_tone = get_conversation_tone(db, transcription.id)

                accuracy_info = {
                    "overall_assessment": ia.overall_assessment[:100] + "..."
                        if ia.overall_assessment and len(ia.overall_assessment) > 100
                        else ia.overall_assessment,
                    "missed_count": len(ia.missed_information) if ia.missed_information else 0,
                    "incorrect_count": len(ia.incorrect_information) if ia.incorrect_information else 0,
                    "accuracy_percentage": ia.accuracy_percentage if ia.accuracy_percentage is not None else "NA",
                    "conversation_flags": conversation_flags,
                    "conversation_tone": conversation_tone
                }

            # Add transcription details to call info
            call_info["transcription"] = {
                "id": transcription.id,
                "text_preview": transcription.text[:150] + "..." if len(transcription.text) > 150 else transcription.text,
                "duration_seconds": 0.0,  # Default value since column was removed from database
                "created_at": transcription.created_at,
                "speaker_count": speaker_count,
                "sentiment_info": sentiment_info,
                "accuracy_info": accuracy_info
            }

        return call_info

    except Exception as e:
        logger.error(f"Error retrieving call data detail by call_id from database: {str(e)}")
        return None


def get_enhanced_call_data_by_id(db: Session, id: str) -> Optional[Dict[str, Any]]:
    """
    Get enhanced call data by database ID with full transcription information including complete text and speaker segments.

    Args:
        db: Database session
        id: Database ID of the call record

    Returns:
        Optional[Dict[str, Any]]: Enhanced call data dictionary with complete transcription details if found, None otherwise
    """
    try:
        # Query call data with transcription join
        call_data = db.query(CallData).outerjoin(
            Transcription, CallData.transcription_id == Transcription.id
        ).filter(CallData.id == id).first()

        if not call_data:
            return None

        # Create base call data dictionary with additional fields
        call_info = {
            "id": call_data.id,
            "call_id": call_data.call_id,
            "call_duration": call_data.call_duration,
            "audio_url": call_data.audio_url,
            "processed": call_data.processed,
            "created_at": call_data.created_at,
            "transcription_id": call_data.transcription_id,
            "transcription": None,
            "agent_name": call_data.agent_name,
            "call_date": call_data.call_date,
            "call_time": call_data.call_time,
            "group_name": call_data.group_name,
            "student_id": call_data.student_id,
            "student_name": call_data.student_name,
            "call_direction": call_data.call_direction,
            "call_source": call_data.call_source
        }

        # Add transcription details if available
        if call_data.transcription_id and call_data.transcription:
            transcription = call_data.transcription

            # Get full speaker segments
            speaker_segments = []
            if transcription.speaker_segments:
                for segment in transcription.speaker_segments:
                    speaker_segments.append({
                        "speaker": segment.speaker,
                        "text": segment.text
                    })

            # Get sentiment info if available
            sentiment_info = None
            if transcription.sentiment_analysis:
                sa = transcription.sentiment_analysis
                sentiment_info = {
                    "overall_sentiment": sa.overall_sentiment,
                    "is_student_university_conversation": sa.is_student_university_conversation,
                    "relevant_category": sa.relevant_category,
                    "summary": sa.summary,
                    "key_points": sa.key_points,
                    "speaker_roles": sa.speaker_roles,
                    "action_items": sa.action_items
                }

            # Get complete information accuracy if available
            accuracy_info = None
            if transcription.information_accuracy:
                ia = transcription.information_accuracy

                # Get conversation flags from the separate table
                conversation_flags = {
                    "satisfaction": False,
                    "confusion": False,
                    "urgency": False,
                    "frustration": False,
                    "abusive": False
                }

                # Query the conversation flags for this transcription
                flag_records = db.query(ConversationFlagDB).filter(
                    ConversationFlagDB.transcription_id == transcription.id
                ).all()

                for flag_record in flag_records:
                    if flag_record.flag_type and flag_record.is_present:
                        conversation_flags[flag_record.flag_type.value] = True

                # Get conversation tone
                conversation_tone = get_conversation_tone(db, transcription.id)

                accuracy_info = {
                    "overall_assessment": ia.overall_assessment,
                    "missed_information": ia.missed_information,
                    "incorrect_information": ia.incorrect_information,
                    "incomplete_information": ia.incomplete_information,
                    "correct_information": ia.correct_information,
                    "accuracy_percentage": ia.accuracy_percentage if ia.accuracy_percentage is not None else 0.0,
                    "conversation_flags": conversation_flags,
                    "conversation_tone": conversation_tone,
                    "created_at": ia.created_at
                }

            # Add complete transcription details to call info
            call_info["transcription"] = {
                "id": transcription.id,
                "full_text": transcription.text,  # Include the full text
                "text_preview": transcription.text[:150] + "..." if len(transcription.text) > 150 else transcription.text,
                "duration_seconds": 0.0,  # Default value since column was removed from database
                "created_at": transcription.created_at,
                "speaker_segments": speaker_segments,  # Include all speaker segments
                "speaker_count": len(speaker_segments),
                "sentiment_info": sentiment_info,
                "accuracy_info": accuracy_info,
                "metadata": transcription.transcription_metadata
            }

        return call_info

    except Exception as e:
        logger.error(f"Error retrieving enhanced call data by ID from database: {str(e)}")
        return None

def get_enhanced_call_data_by_call_id(db: Session, call_id: str) -> Optional[Dict[str, Any]]:
    """
    Get enhanced call data by call_id with full transcription information including complete text and speaker segments.

    Args:
        db: Database session
        call_id: Call ID (not database ID)

    Returns:
        Optional[Dict[str, Any]]: Enhanced call data dictionary with complete transcription details if found, None otherwise
    """
    try:
        # Query call data with transcription join
        call_data = db.query(CallData).outerjoin(
            Transcription, CallData.transcription_id == Transcription.id
        ).filter(CallData.call_id == call_id).first()

        if not call_data:
            return None

        # Create base call data dictionary with additional fields
        call_info = {
            "id": call_data.id,
            "call_id": call_data.call_id,
            "call_duration": call_data.call_duration,
            "audio_url": call_data.audio_url,
            "processed": call_data.processed,
            "created_at": call_data.created_at,
            "transcription_id": call_data.transcription_id,
            "transcription": None,
            "agent_name": call_data.agent_name,
            "call_date": call_data.call_date,
            "call_time": call_data.call_time,
            "group_name": call_data.group_name,
            "student_id": call_data.student_id,
            "student_name": call_data.student_name,
            "call_direction": call_data.call_direction,
            "call_source": call_data.call_source
        }

        # Add transcription details if available
        if call_data.transcription_id and call_data.transcription:
            transcription = call_data.transcription

            # Get full speaker segments
            speaker_segments = []
            if transcription.speaker_segments:
                for segment in transcription.speaker_segments:
                    speaker_segments.append({
                        "speaker": segment.speaker,
                        "text": segment.text
                    })

            # Get sentiment info if available
            sentiment_info = None
            if transcription.sentiment_analysis:
                sa = transcription.sentiment_analysis
                sentiment_info = {
                    "overall_sentiment": sa.overall_sentiment,
                    "is_student_university_conversation": sa.is_student_university_conversation,
                    "relevant_category": sa.relevant_category,
                    "summary": sa.summary,
                    "key_points": sa.key_points,
                    "speaker_roles": sa.speaker_roles,
                    "action_items": sa.action_items
                }

            # Get complete information accuracy if available
            accuracy_info = None
            if transcription.information_accuracy:
                ia = transcription.information_accuracy

                # Get conversation flags from the separate table
                conversation_flags = {
                    "satisfaction": False,
                    "confusion": False,
                    "urgency": False,
                    "frustration": False,
                    "abusive": False
                }

                # Query the conversation flags for this transcription
                flag_records = db.query(ConversationFlagDB).filter(
                    ConversationFlagDB.transcription_id == transcription.id
                ).all()

                for flag_record in flag_records:
                    if flag_record.flag_type and flag_record.is_present:
                        conversation_flags[flag_record.flag_type.value] = True

                # Get conversation tone
                conversation_tone = get_conversation_tone(db, transcription.id)

                accuracy_info = {
                    "overall_assessment": ia.overall_assessment,
                    "missed_information": ia.missed_information,
                    "incorrect_information": ia.incorrect_information,
                    "incomplete_information": ia.incomplete_information,
                    "correct_information": ia.correct_information,
                    "accuracy_percentage": ia.accuracy_percentage if ia.accuracy_percentage is not None else 0.0,
                    "conversation_flags": conversation_flags,
                    "conversation_tone": conversation_tone,
                    "created_at": ia.created_at
                }

            # Add complete transcription details to call info
            call_info["transcription"] = {
                "id": transcription.id,
                "full_text": transcription.text,  # Include the full text
                "text_preview": transcription.text[:150] + "..." if len(transcription.text) > 150 else transcription.text,
                "duration_seconds": 0.0,  # Default value since column was removed from database
                "created_at": transcription.created_at,
                "speaker_segments": speaker_segments,  # Include all speaker segments
                "speaker_count": len(speaker_segments),
                "sentiment_info": sentiment_info,
                "accuracy_info": accuracy_info,
                "metadata": transcription.transcription_metadata
            }

        return call_info

    except Exception as e:
        logger.error(f"Error retrieving enhanced call data by call_id from database: {str(e)}")
        return None


def count_call_data(db: Session, filters: Optional[CallFilters] = None) -> int:
    """
    Count the total number of call data records, with optional filtering.

    Args:
        db: Database session
        filters: Optional CallFilters object for filtering results

    Returns:
        Total number of call data records matching the filters
    """
    try:
        # Build base query
        query = db.query(CallData)

        # Apply filters if provided
        if filters:
            conditions, sentiment_conditions, flag_conditions = _build_filter_conditions(filters)

            # Apply basic call data conditions
            for condition in conditions:
                query = query.filter(condition)

            # Join with Transcription if we need sentiment or flag filtering
            if sentiment_conditions or flag_conditions:
                query = query.outerjoin(
                    Transcription, CallData.transcription_id == Transcription.id
                )

            # Apply sentiment-based filters (requires join with sentiment analysis)
            if sentiment_conditions:
                query = query.outerjoin(
                    SentimentAnalysisDB, Transcription.id == SentimentAnalysisDB.transcription_id
                )
                for condition in sentiment_conditions:
                    query = query.filter(condition)

            # Apply flag-based filters (requires join with conversation flags)
            if flag_conditions:
                query = query.outerjoin(
                    ConversationFlagDB, Transcription.id == ConversationFlagDB.transcription_id
                )
                for condition in flag_conditions:
                    query = query.filter(condition)

        return query.count()
    except Exception as e:
        logger.error(f"Error counting call data records: {str(e)}")
        return 0


def get_all_call_data(db: Session, skip: int = 0, limit: int = 100, filters: Optional[CallFilters] = None) -> List[Dict[str, Any]]:
    """
    Get all call data with their associated transcription details, with optional filtering.

    Args:
        db: Database session
        skip: Number of records to skip
        limit: Maximum number of records to return
        filters: Optional CallFilters object for filtering results

    Returns:
        List of call data dictionaries with transcription details
    """
    try:
        # Build base query with transcription join
        query = db.query(CallData).outerjoin(
            Transcription, CallData.transcription_id == Transcription.id
        )

        # Apply filters if provided
        if filters:
            conditions, sentiment_conditions, flag_conditions = _build_filter_conditions(filters)

            # Apply basic call data conditions
            for condition in conditions:
                query = query.filter(condition)

            # Apply sentiment-based filters (requires join with sentiment analysis)
            if sentiment_conditions:
                query = query.outerjoin(
                    SentimentAnalysisDB, Transcription.id == SentimentAnalysisDB.transcription_id
                )
                for condition in sentiment_conditions:
                    query = query.filter(condition)

            # Apply flag-based filters (requires join with conversation flags)
            if flag_conditions:
                query = query.outerjoin(
                    ConversationFlagDB, Transcription.id == ConversationFlagDB.transcription_id
                )
                for condition in flag_conditions:
                    query = query.filter(condition)

        # Execute query with pagination
        call_data_records = query.order_by(CallData.created_at.desc()).offset(skip).limit(limit).all()

        result = []
        for call_data in call_data_records:
            # Create base call data dictionary with additional fields
            call_info = {
                "id": call_data.id,
                "call_id": call_data.call_id,
                "call_duration": call_data.call_duration,
                "audio_url": call_data.audio_url,
                "processed": call_data.processed,
                "created_at": call_data.created_at,
                "transcription_id": call_data.transcription_id,
                "transcription": None,
                "agent_name": call_data.agent_name,
                "call_date": call_data.call_date,
                "call_time": call_data.call_time,
                "group_name": call_data.group_name,
                "student_id": call_data.student_id,
                "student_name": call_data.student_name,
                "call_direction": call_data.call_direction,
                "call_source": call_data.call_source
            }

            # Add transcription details if available
            if call_data.transcription_id and call_data.transcription:
                transcription = call_data.transcription

                # Get speaker segments count
                speaker_count = len(transcription.speaker_segments) if transcription.speaker_segments else 0

                # Get sentiment info if available
                sentiment_info = None
                if transcription.sentiment_analysis:
                    sa = transcription.sentiment_analysis
                    sentiment_info = {
                        "overall_sentiment": sa.overall_sentiment,
                        "is_student_university_conversation": sa.is_student_university_conversation,
                        "summary": sa.summary
                    }

                # Get information accuracy if available
                accuracy_info = None
                if transcription.information_accuracy:
                    ia = transcription.information_accuracy

                    # Get conversation flags from the new separate table
                    conversation_flags = {
                        "satisfaction": False,
                        "confusion": False,
                        "urgency": False,
                        "frustration": False,
                        "abusive": False
                    }

                    # Query the conversation flags for this transcription
                    flag_records = db.query(ConversationFlagDB).filter(
                        ConversationFlagDB.transcription_id == transcription.id
                    ).all()

                    for flag_record in flag_records:
                        if flag_record.flag_type and flag_record.is_present:
                            conversation_flags[flag_record.flag_type.value] = True

                    # Get conversation tone
                    conversation_tone = get_conversation_tone(db, transcription.id)

                    accuracy_info = {
                        "overall_assessment": ia.overall_assessment,
                        "missed_count": (
                            len(ia.missed_information) if ia.missed_information else 0
                        ),
                        "incorrect_count": (
                            len(ia.incorrect_information)
                            if ia.incorrect_information
                            else 0
                        ),
                        "missed_information": ia.missed_information,
                        "incorrect_information": ia.incorrect_information,
                        "incomplete_information": ia.incomplete_information,
                        "correct_information": ia.correct_information,
                        "accuracy_percentage": ia.accuracy_percentage if ia.accuracy_percentage is not None else "NA",
                        "conversation_flags": conversation_flags,
                        "conversation_tone": conversation_tone
                    }

                # Add transcription details to call info
                call_info["transcription"] = {
                    "id": transcription.id,
                    "text_preview": transcription.text[:150] + "..." if len(transcription.text) > 150 else transcription.text,
                    "duration_seconds": 0.0,  # Default value since column was removed from database
                    "created_at": transcription.created_at,
                    "speaker_count": speaker_count,
                    "sentiment_info": sentiment_info,
                    "accuracy_info": accuracy_info
                }

            result.append(call_info)

        return result

    except Exception as e:
        logger.error(f"Error retrieving call data from database: {str(e)}")
        return []


def get_transcription_by_id(db: Session, transcription_id: str) -> Optional[TranscriptionResponse]:
    """
    Get a transcription by ID.

    Args:
        db: Database session
        transcription_id: ID of the transcription

    Returns:
        Optional[TranscriptionResponse]: TranscriptionResponse object if found, None otherwise
    """
    try:
        # Query the transcription with related data
        transcription = db.query(Transcription).filter(Transcription.id == transcription_id).first()

        if not transcription:
            return None

        # Convert to TranscriptionResponse
        from app.models.transcription import SentimentLevel, ConversationRole

        # Convert speaker segments
        speaker_segments = []
        for segment in transcription.speaker_segments:
            speaker_segments.append(SpeakerSegment(
                speaker=segment.speaker,
                text=segment.text
            ))

        # Convert sentiment analysis if available
        sentiment_analysis = None
        if transcription.sentiment_analysis:
            sa = transcription.sentiment_analysis

            # Convert string values back to enums
            sentiment_analysis = SentimentAnalysis(
                is_student_university_conversation=sa.is_student_university_conversation,
                overall_sentiment=SentimentLevel(sa.overall_sentiment) if sa.overall_sentiment else SentimentLevel.NEUTRAL,
                key_points=sa.key_points or [],
                speaker_roles={k: ConversationRole(v) for k, v in sa.speaker_roles.items()} if sa.speaker_roles else {},
                summary=sa.summary or "",
                action_items=sa.action_items or []
            )

        # Convert information accuracy if available
        information_accuracy = None
        if transcription.information_accuracy:
            ia = transcription.information_accuracy

            # Get conversation flags from the separate table
            conversation_flags = {
                "satisfaction": False,
                "confusion": False,
                "urgency": False,
                "frustration": False,
                "abusive": False
            }

            # Query the conversation flags for this transcription
            flag_records = db.query(ConversationFlagDB).filter(
                ConversationFlagDB.transcription_id == transcription.id
            ).all()

            for flag_record in flag_records:
                if flag_record.flag_type and flag_record.is_present:
                    conversation_flags[flag_record.flag_type.value] = True

            # Get conversation tone
            conversation_tone = get_conversation_tone(db, transcription.id)

            information_accuracy = InformationAccuracy(
                missed_information=ia.missed_information or [],
                incorrect_information=ia.incorrect_information or [],
                incomplete_information=ia.incomplete_information or [],
                correct_information=ia.correct_information or [],
                overall_assessment=ia.overall_assessment or "",
                accuracy_percentage=ia.accuracy_percentage if ia.accuracy_percentage is not None else 0.0,
                conversation_flags=conversation_flags,
                conversation_tone=conversation_tone,
                created_at=ia.created_at
            )

        # Create the response
        return TranscriptionResponse(
            id=transcription.id,
            text=transcription.text,
            duration_seconds=0.0,  # Default value since column was removed from database
            metadata=transcription.transcription_metadata or {},
            created_at=transcription.created_at,
            speaker_segments=speaker_segments,
            sentiment_analysis=sentiment_analysis,
            information_accuracy=information_accuracy
        )

    except Exception as e:
        logger.error(f"Error retrieving transcription from database: {str(e)}")
        return None
