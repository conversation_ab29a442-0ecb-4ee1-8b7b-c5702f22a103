import asyncio
import logging
from datetime import datetime, timedelta, timezone
from typing import Optional, Dict, Any
from sqlalchemy.orm import Session

from app.core.settings import settings
from app.db.database import SessionLocal
from app.db.models import CronJobExecutionDB
from app.models.cron_job import (
    CronJobExecution, CronJobStatus as CronJobStatusEnum,
    CronJobType, CronJobExecutionResponse
)
from app.services.call_data_service import fetch_call_data, filter_valid_calls, process_calls_concurrently
from app.services.email_reporting_service import EmailReportingService
from app.services.health_check_service import health_check_service
from app.services.email_service import send_simple_email,send_email_to_multiple_recipients
from app.models.email import EmailRequest
from app.utils.date_formatting import format_current_datetime_general

logger = logging.getLogger(__name__)


def get_previous_day_date_range() -> tuple[str, str]:
    """
    Calculate the previous day's date range for call processing.

    Returns:
        tuple: (start_date, end_date) in format 'YYYY-MM-DD HH:MM:SS'
    """
    # Get current date in UTC
    now = datetime.now(timezone.utc)

    # Calculate previous day
    previous_day = now - timedelta(days=1)

    # Set start time to 00:00:00 of previous day
    start_date = previous_day.replace(hour=0, minute=0, second=0, microsecond=0)

    # Set end time to 00:00:00 of current day (start of today)
    end_date = now.replace(hour=0, minute=0, second=0, microsecond=0)

    # Format as strings
    start_date_str = start_date.strftime("%Y-%m-%d %H:%M:%S")
    end_date_str = end_date.strftime("%Y-%m-%d %H:%M:%S")

    return start_date_str, end_date_str


def get_custom_date_range(date_str: str) -> tuple[str, str]:
    """
    Calculate date range for a specific date.

    Args:
        date_str: Date in format 'YYYY-MM-DD'

    Returns:
        tuple: (start_date, end_date) in format 'YYYY-MM-DD HH:MM:SS'
    """
    try:
        # Parse the date
        target_date = datetime.strptime(date_str, "%Y-%m-%d")

        # Set start time to 00:00:00 of target day
        start_date = target_date.replace(hour=0, minute=0, second=0, microsecond=0)

        # Set end time to 00:00:00 of next day
        end_date = start_date + timedelta(days=1)

        # Format as strings
        start_date_str = start_date.strftime("%Y-%m-%d %H:%M:%S")
        end_date_str = end_date.strftime("%Y-%m-%d %H:%M:%S")

        return start_date_str, end_date_str

    except ValueError as e:
        raise ValueError(f"Invalid date format '{date_str}'. Expected YYYY-MM-DD format.") from e


def save_cron_job_execution(db: Session, execution: CronJobExecution) -> CronJobExecutionDB:
    """
    Save cron job execution to database.

    Args:
        db: Database session
        execution: CronJobExecution to save

    Returns:
        CronJobExecutionDB: Saved database record
    """
    try:
        db_execution = CronJobExecutionDB(
            job_type=execution.job_type.value,
            status=execution.status.value,
            start_time=execution.start_time,
            end_time=execution.end_time,
            duration_seconds=execution.duration_seconds,
            date_range_start=execution.date_range_start,
            date_range_end=execution.date_range_end,
            calls_processed=execution.calls_processed,
            calls_found=execution.calls_found,
            max_concurrent=execution.max_concurrent,
            error_message=execution.error_message,
            error_details=execution.error_details,
            triggered_by=execution.triggered_by,
            job_metadata=execution.metadata
        )

        db.add(db_execution)
        db.commit()
        db.refresh(db_execution)

        return db_execution

    except Exception as e:
        db.rollback()
        logger.error(f"Error saving cron job execution: {str(e)}")
        raise


def get_last_cron_job_execution(db: Session, job_type: CronJobType) -> Optional[CronJobExecution]:
    """
    Get the last execution record for a specific job type.

    Args:
        db: Database session
        job_type: Type of cron job

    Returns:
        Optional[CronJobExecution]: Last execution record if found
    """
    try:
        last_execution = db.query(CronJobExecutionDB).filter(
            CronJobExecutionDB.job_type == job_type.value
        ).order_by(CronJobExecutionDB.start_time.desc()).first()

        if not last_execution:
            return None

        return CronJobExecution(
            id=last_execution.id,
            job_type=CronJobType(last_execution.job_type),
            status=CronJobStatusEnum(last_execution.status),
            start_time=last_execution.start_time,
            end_time=last_execution.end_time,
            duration_seconds=last_execution.duration_seconds,
            date_range_start=last_execution.date_range_start,
            date_range_end=last_execution.date_range_end,
            calls_processed=last_execution.calls_processed,
            calls_found=last_execution.calls_found,
            max_concurrent=last_execution.max_concurrent,
            error_message=last_execution.error_message,
            error_details=last_execution.error_details,
            triggered_by=last_execution.triggered_by,
            metadata=last_execution.job_metadata
        )

    except Exception as e:
        logger.error(f"Error getting last cron job execution: {str(e)}")
        return None


async def execute_daily_call_processing(
    date_override: Optional[str] = None,
    max_concurrent_override: Optional[int] = None,
    triggered_by: str = "cron"
) -> CronJobExecutionResponse:
    """
    Execute daily call processing job.

    Args:
        date_override: Optional date override in YYYY-MM-DD format
        max_concurrent_override: Optional max concurrent tasks override
        triggered_by: How the job was triggered

    Returns:
        CronJobExecutionResponse: Job execution result
    """
    # Create execution record
    execution = CronJobExecution(
        job_type=CronJobType.DAILY_CALL_PROCESSING,
        status=CronJobStatusEnum.RUNNING,
        triggered_by=triggered_by
    )

    db = SessionLocal()

    try:
        # Calculate date range
        if date_override:
            start_date, end_date = get_custom_date_range(date_override)
            logger.info(f"Using custom date range: {start_date} to {end_date}")
        else:
            start_date, end_date = get_previous_day_date_range()
            logger.info(f"Using previous day date range: {start_date} to {end_date}")

        execution.date_range_start = start_date
        execution.date_range_end = end_date

        # Set max concurrent
        max_concurrent = max_concurrent_override or settings.CRON_MAX_CONCURRENT
        execution.max_concurrent = max_concurrent

        logger.info(f"Starting daily call processing job: {start_date} to {end_date} (max_concurrent: {max_concurrent})")

        # Save initial execution record
        db_execution = save_cron_job_execution(db, execution)
        execution.id = db_execution.id

        # Check API health before processing
        logger.info("Checking API health before processing...")
        health_result = await health_check_service.check_all_apis()

        if not health_result.all_healthy:
            # Send failure notification email
            await _send_api_health_failure_email(health_result)

            # Update execution status and raise exception
            execution.status = CronJobStatusEnum.FAILED
            execution.error_message = f"API health check failed: {', '.join(health_result.failed_services)}"
            _update_cron_job_execution(db, execution)

            raise Exception(f"API health check failed. Failed services: {', '.join(health_result.failed_services)}")

        logger.info("All APIs are healthy. Proceeding with call processing...")

        # Fetch call data from MCube API
        logger.info(f"Fetching call data from MCube API...")
        call_data_response = await fetch_call_data(start_date, end_date)

        # Check if the API request was successful
        if call_data_response.status != "succ":
            raise Exception(f"Failed to fetch call data: {call_data_response.status}")

        # Filter valid calls
        valid_calls = filter_valid_calls(call_data_response.msg)
        execution.calls_found = len(valid_calls)

        logger.info(f"Found {len(valid_calls)} valid calls to process")

        if not valid_calls:
            # No calls to process
            execution.status = CronJobStatusEnum.COMPLETED
            execution.calls_processed = 0
            execution.end_time = datetime.now()
            execution.duration_seconds = (execution.end_time - execution.start_time).total_seconds()

            # Update database record
            db_execution.status = execution.status.value
            db_execution.calls_found = execution.calls_found
            db_execution.calls_processed = execution.calls_processed
            db_execution.end_time = execution.end_time
            db_execution.duration_seconds = execution.duration_seconds
            db.commit()

            message = f"No valid calls found for date range {start_date} to {end_date}"
            logger.info(message)

            return CronJobExecutionResponse(
                success=True,
                message=message,
                execution=execution
            )

        # Process calls concurrently
        logger.info(f"Processing {len(valid_calls)} calls with max concurrency of {max_concurrent}")
        processed_calls = await process_calls_concurrently(
            calls=valid_calls,
            db=db,
            max_concurrent_tasks=max_concurrent
        )

        # Commit the database session
        db.commit()

        # Update execution record
        execution.status = CronJobStatusEnum.COMPLETED
        execution.calls_processed = len(processed_calls)
        execution.end_time = datetime.now()
        execution.duration_seconds = (execution.end_time - execution.start_time).total_seconds()

        # Update database record
        db_execution.status = execution.status.value
        db_execution.calls_found = execution.calls_found
        db_execution.calls_processed = execution.calls_processed
        db_execution.end_time = execution.end_time
        db_execution.duration_seconds = execution.duration_seconds
        db.commit()

        message = f"Successfully processed {len(processed_calls)} out of {len(valid_calls)} calls"
        logger.info(f"Daily call processing job completed: {message}")

        # Trigger email reporting after successful completion
        try:
            logger.info("Starting automated email reporting for agents...")
            email_service = EmailReportingService()
            email_result = await email_service.generate_and_send_agent_reports(
                date_range_start=start_date,
                date_range_end=end_date
            )

            if email_result.success:
                logger.info(f"Email reporting completed successfully: {email_result.reports_generated} reports generated, {email_result.emails_sent} emails sent")
                message += f" | Email reports: {email_result.reports_generated} generated, {email_result.emails_sent} sent"
            else:
                logger.warning(f"Email reporting completed with issues: {email_result.message}")
                if email_result.errors:
                    for error in email_result.errors[:3]:  # Log first 3 errors
                        logger.warning(f"Email reporting error: {error}")
                message += f" | Email reports: {email_result.reports_generated} generated, {email_result.emails_sent} sent (with issues)"

        except Exception as email_error:
            logger.error(f"Email reporting failed: {str(email_error)}")
            message += " | Email reporting failed"

        return CronJobExecutionResponse(
            success=True,
            message=message,
            execution=execution
        )

    except Exception as e:
        # Update execution record with error
        execution.status = CronJobStatusEnum.FAILED
        execution.error_message = str(e)
        execution.end_time = datetime.now()
        execution.duration_seconds = (execution.end_time - execution.start_time).total_seconds()

        # Update database record if it exists
        if execution.id:
            try:
                db_execution = db.query(CronJobExecutionDB).filter(
                    CronJobExecutionDB.id == execution.id
                ).first()
                if db_execution:
                    db_execution.status = execution.status.value
                    db_execution.error_message = execution.error_message
                    db_execution.end_time = execution.end_time
                    db_execution.duration_seconds = execution.duration_seconds
                    db.commit()
            except Exception as db_error:
                logger.error(f"Error updating failed execution record: {str(db_error)}")

        logger.error(f"Daily call processing job failed: {str(e)}")

        return CronJobExecutionResponse(
            success=False,
            message=f"Job failed: {str(e)}",
            execution=execution
        )

    finally:
        db.close()


async def _send_api_health_failure_email(health_result) -> None:
    """
    Send email notification when API health check fails.

    Args:
        health_result: HealthCheckResult containing failed API information
    """
    try:
        # Format the health report
        health_report = health_check_service.format_health_report(health_result)

        # Create email content
        current_time = format_current_datetime_general()

        subject = f"🚨 API Health Check Failed - Call Processing Job Stopped ({current_time})"

        message = f"""
API Health Check Failure Notification

Time: {current_time}
Status: FAILED

{health_report}

The daily call processing job has been stopped due to API health check failures.
Please investigate and resolve the API connectivity issues before the next scheduled run.

Failed Services: {', '.join(health_result.failed_services)}

This is an automated notification from the Recording Sentiment Detection System.
"""

        # Send email to specified recipients
        recipients = [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>"
        ]

        try:
            response = send_email_to_multiple_recipients(recipients, subject, message)
            if response.success:
                    logger.info(f"API health failure notification sent to {recipients}")
            else:
                    logger.error(f"Failed to send API health failure notification to {recipients}: {response.message}")
        except Exception as email_error:
                logger.error(f"Error sending API health failure notification to {recipients}: {str(email_error)}")

    except Exception as e:
        logger.error(f"Error sending API health failure email: {str(e)}")


def _update_cron_job_execution(db: Session, execution: CronJobExecution) -> None:
    """
    Update cron job execution record in database.

    Args:
        db: Database session
        execution: CronJobExecution object to update
    """
    try:
        if execution.id:
            db_execution = db.query(CronJobExecutionDB).filter(
                CronJobExecutionDB.id == execution.id
            ).first()
            if db_execution:
                db_execution.status = execution.status.value
                db_execution.error_message = execution.error_message
                db_execution.end_time = execution.end_time
                db_execution.duration_seconds = execution.duration_seconds
                db.commit()
                logger.debug(f"Updated cron job execution record: {execution.id}")
    except Exception as e:
        logger.error(f"Error updating cron job execution record: {str(e)}")
        db.rollback()
